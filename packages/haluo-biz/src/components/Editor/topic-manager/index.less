@import "@/assets/css/util";
@brand-color: #FF3C08; // 品牌色（用于多出突出主体的位置) 按钮色值、底部标签栏当前模块色值等

// 话题弹框样式
.topic-popover-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    background: transparent;
}

.topic-popover {
    position: fixed;
    z-index: 9999;
    background: #fff;
    border-radius: 4px;
    box-sizing: border-box;
    box-shadow: 0 4px 17px rgba(0, 0, 0, .12);
    width: 430px;
    height: 286px;
    overflow-y: auto;

    .topic-popover-content {
        .topic-tabs {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            background: #fafafa;

            .topic-tab {
                flex: 1;
                padding: 12px 16px;
                text-align: center;
                font-size: 14px;
                color: #666;
                cursor: pointer;
                border-bottom: 2px solid transparent;
                transition: all 0.2s;

                &:hover {
                    color: @brand-color;
                    background: #f5f5f5;
                }

                &.active {
                    color: @brand-color;
                    border-bottom-color: @brand-color;
                    background: #fff;
                }
            }
        }

        .topic-search-header {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
            background: #fafafa;

            .topic-search-title {
                font-size: 14px;
                color: #333;
                font-weight: 500;
            }
        }

        .topic-list-container {
            max-height: 240px;
            overflow-y: auto;

            .topic-list {
                .topic-item {
                    padding: 12px 16px;
                    font-size: 14px;
                    color: #333;
                    cursor: pointer;
                    border-bottom: 1px solid #f8f8f8;
                    transition: background-color 0.2s;

                    &:hover {
                        background: #f5f5f5;
                        color: @brand-color;
                    }

                    &:last-child {
                        border-bottom: none;
                    }
                }

                .topic-loading,
                .topic-empty {
                    padding: 20px 16px;
                    text-align: center;
                    font-size: 14px;
                    color: #999;
                }
            }
        }
    }
}