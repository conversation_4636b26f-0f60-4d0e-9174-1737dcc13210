<template>
  <div class="topic-manager"></div>
</template>

<script>
export default {
  name: 'TopicManager',
  props: {
    // 编辑器DOM引用
    editorDom: {
      type: Object,
      default: null
    },
    // 话题API请求方法
    request: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      // 话题弹框相关数据
      topicPopover: {
        visible: false,
        type: 'hot', // 'hot' 热门话题, 'search' 搜索话题
        activeTab: 'hot', // 'hot' 热门话题, 'recent' 最近使用
        position: { top: 0, left: 0 },
        searchKeyword: '',
        hotTopics: [],
        searchTopics: [],
        recentTopics: [],
        loading: false,
        page: 1,
        hasMore: true,
        searchPage: 1,
        searchHasMore: true,
        // 保存原始的selection和range信息
        originalRange: null,
        originalSelection: null,
        // 保存触发位置信息
        triggerInfo: {
          paragraph: null,
          hashIndex: -1,
          cursorPosition: 0
        },
        // 全局弹框DOM引用
        globalContainer: null,
        globalMask: null
      }
    }
  },
  methods: {
    // 初始化话题管理器
    init() {
      if (!this.editorDom) {
        console.warn('TopicManager: editorDom is required')
        return
      }
      
      // 绑定编辑器事件
      setTimeout(() => this.bindEditorEvents(), 0)
    },

    // 绑定编辑器事件
    bindEditorEvents() {
      if (!this.editorDom) return

      // 绑定键盘事件
      this.editorDom.addEventListener('keydown', this.handleTopicInput.bind(this))
    },

    // 解绑编辑器事件
    unbindEditorEvents() {
      if (!this.editorDom) return

      this.editorDom.removeEventListener('keydown', this.handleTopicInput.bind(this))
    },

    // 话题输入处理
    handleTopicInput(event) {
      // 不在input或textarea中处理
      const activeElement = document.activeElement
      if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
        return
      }

      const selection = window.getSelection()
      if (selection.rangeCount === 0) return

      const range = selection.getRangeAt(0)
      const container = range.startContainer

      // 确保在编辑器内
      if (!this.editorDom.contains(container)) return

      // 检查是否在mdd-topic元素内，如果是则不处理
      let currentNode = container
      while (currentNode && currentNode !== this.editorDom) {
        if (currentNode.nodeType === Node.ELEMENT_NODE && currentNode.tagName === 'MDD-TOPIC') {
          return
        }
        currentNode = currentNode.parentNode
      }

      // 获取当前段落
      let paragraph = container
      while (paragraph && paragraph.nodeType !== Node.ELEMENT_NODE) {
        paragraph = paragraph.parentNode
      }
      while (paragraph && !paragraph.classList?.contains('halo-paragraph')) {
        paragraph = paragraph.parentNode
      }

      if (!paragraph) return

      // 获取不包含mdd-topic内部文本的段落文本
      const { textContent: paragraphText, cursorPosition } = this.getParagraphTextExcludingTopics(paragraph, range)

      if (event.key === '#') {
        // 检查是否是 # 或 #@ 这种情况，不触发弹框
        const afterCursor = paragraphText.substring(cursorPosition)

        // 如果#号后面紧跟@或空格，则不触发
        if (afterCursor.length > 0 && (afterCursor[0] === '@' || afterCursor[0] === ' ')) {
          return
        }

        // 延迟处理，确保#号已经输入到DOM中
        setTimeout(() => {
          const position = this.getCaretPosition()
          const triggerInfo = {
            paragraph: paragraph,
            hashIndex: cursorPosition, // #号的位置
            cursorPosition: cursorPosition + 1 // #号后的位置
          }
          this.showTopicPopover('hot', position, '', triggerInfo)
        }, 10)
      } else if (this.shouldTriggerSearch(event.key)) {
        // 输入其他字符或删除字符，检查是否需要显示搜索弹框
        setTimeout(() => {
          this.checkAndTriggerSearch(paragraph)
        }, 10)
      }
    },

    shouldTriggerSearch(key) {
      // 可打印字符、退格键、删除键都可能触发搜索
      return key.length === 1 || key === 'Backspace' || key === 'Delete'
    },

    checkAndTriggerSearch(paragraph) {
      const selection = window.getSelection()
      if (selection.rangeCount === 0) return

      const range = selection.getRangeAt(0)

      // 检查光标是否在mdd-topic元素内部，如果是则不触发搜索
      let currentNode = range.startContainer
      while (currentNode && currentNode !== paragraph) {
        if (currentNode.nodeType === Node.ELEMENT_NODE && currentNode.tagName === 'MDD-TOPIC') {
          this.hideTopicPopover()
          return
        }
        currentNode = currentNode.parentNode
      }

      // 获取不包含mdd-topic内部文本的段落文本
      const { textContent: paragraphText, cursorPosition } = this.getParagraphTextExcludingTopics(paragraph, range)
      const beforeCursor = paragraphText.substring(0, cursorPosition)
      const hashIndex = beforeCursor.lastIndexOf('#')

      if (hashIndex !== -1) {
        const afterHash = beforeCursor.substring(hashIndex + 1)

        // 检查#号后面是否有空格或@符号，如果有则不触发
        if (afterHash.indexOf(' ') === -1 && afterHash.indexOf('@') === -1) {
          // 检查搜索关键字长度限制
          if (afterHash.length <= 15) {
            const position = this.getCaretPosition()
            const triggerInfo = {
              paragraph: paragraph,
              hashIndex: hashIndex,
              cursorPosition: cursorPosition
            }

            // 根据是否有搜索关键词决定显示哪种弹框
            if (afterHash.length === 0) {
              // 只有#号，没有搜索关键词，显示热门话题弹框
              this.showTopicPopover('hot', position, '', triggerInfo)
            } else {
              // 有搜索关键词，显示搜索话题弹框
              this.showTopicPopover('search', position, afterHash, triggerInfo)
            }
          } else if (afterHash.length > 15) {
            // 超过15个字符，隐藏弹框
            this.hideTopicPopover()
          }
        } else {
          // 有空格或@符号，隐藏弹框
          this.hideTopicPopover()
        }
      } else {
        // 没有找到#号，隐藏弹框
        this.hideTopicPopover()
      }
    },

    getParagraphTextExcludingTopics(paragraph, range) {
      let textContent = ''
      let cursorPosition = 0
      let foundCursor = false

      const walker = document.createTreeWalker(
        paragraph,
        NodeFilter.SHOW_ALL,
        {
          acceptNode: function(node) {
            // 跳过mdd-topic元素及其子节点
            if (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'MDD-TOPIC') {
              return NodeFilter.FILTER_REJECT
            }
            return NodeFilter.FILTER_ACCEPT
          }
        },
        false
      )

      let node
      while (node = walker.nextNode()) {
        if (node.nodeType === Node.TEXT_NODE) {
          const nodeText = node.textContent

          // 检查光标是否在当前文本节点中
          if (!foundCursor && node === range.startContainer) {
            cursorPosition = textContent.length + range.startOffset
            foundCursor = true
          }

          textContent += nodeText
        }
      }

      return { textContent, cursorPosition }
    },

    getCaretPosition() {
      const selection = window.getSelection()
      if (selection.rangeCount === 0) return { top: 0, left: 0 }

      const range = selection.getRangeAt(0)
      const rect = range.getBoundingClientRect()

      // 返回相对于视口的位置，用于fixed定位
      return {
        top: rect.bottom + window.scrollY,
        left: rect.left + window.scrollX
      }
    },

    // 话题弹框相关方法
    showTopicPopover(type, position, searchKeyword = '', triggerInfo = null) {
      // 检查必要的依赖
      if (!this.request || !this.request.getTopic) {
        console.warn('话题功能需要提供 request.getTopic 方法')
        return
      }

      // 保存当前的selection和range信息
      const selection = window.getSelection()
      if (selection.rangeCount > 0) {
        this.topicPopover.originalRange = selection.getRangeAt(0).cloneRange()
        this.topicPopover.originalSelection = selection
      }

      // 保存触发信息
      if (triggerInfo) {
        this.topicPopover.triggerInfo = triggerInfo
      }

      this.topicPopover.visible = true
      this.topicPopover.type = type
      this.topicPopover.position = position
      this.topicPopover.searchKeyword = searchKeyword

      if (type === 'hot') {
        this.topicPopover.activeTab = 'hot'
        this.loadHotTopics()
        this.loadRecentTopics()
      } else if (type === 'search') {
        this.searchTopics(searchKeyword)
      }

      // 创建全局弹框
      this.createGlobalTopicPopover()
    },

    hideTopicPopover() {
      this.topicPopover.visible = false
      this.topicPopover.hotTopics = []
      this.topicPopover.searchTopics = []
      this.topicPopover.page = 1
      this.topicPopover.searchPage = 1
      this.topicPopover.hasMore = true
      this.topicPopover.searchHasMore = true
      // 清理保存的信息
      this.topicPopover.originalRange = null
      this.topicPopover.originalSelection = null
      this.topicPopover.triggerInfo = {
        paragraph: null,
        hashIndex: -1,
        cursorPosition: 0
      }

      // 移除全局弹框
      this.removeGlobalTopicPopover()
    },

    switchTopicTab(tab) {
      this.topicPopover.activeTab = tab
      this.updateGlobalTopicPopover()
    },

    async loadHotTopics() {
      if (this.topicPopover.loading || !this.topicPopover.hasMore) return

      this.topicPopover.loading = true
      try {
        const response = await this.request.getTopic({
          action: '201023',
          page: this.topicPopover.page,
          limit: 50,
          hoopId: 0,
          type: 0,
          orderBy: 'view'
        })

        if (response && response.data && response.data.code === 0) {
          const topics = response.data.data || []
          if (topics.length === 0) {
            this.topicPopover.hasMore = false
          } else {
            // 过滤重复的话题
            const existingIds = new Set(this.topicPopover.hotTopics.map(t => t.id))
            const newTopics = topics.filter(t => !existingIds.has(t.id))

            this.topicPopover.hotTopics = [...this.topicPopover.hotTopics, ...newTopics]
            this.topicPopover.page++

            // 如果没有新话题，标记为没有更多
            if (newTopics.length === 0) {
              this.topicPopover.hasMore = false
            }
          }
        } else {
          console.warn('加载热门话题失败:', response)
          this.topicPopover.hasMore = false
        }
      } catch (error) {
        console.error('加载热门话题失败:', error)
        this.topicPopover.hasMore = false
      } finally {
        this.topicPopover.loading = false
        // 更新全局弹框显示
        this.updateGlobalTopicPopover()
      }
    },

    async searchTopics(keyword) {
      // 强制重置状态，确保每次都触发新的搜索
      this.topicPopover.searchKeyword = keyword
      this.topicPopover.searchTopics = []
      this.topicPopover.searchPage = 1
      this.topicPopover.searchHasMore = true

      // 如果关键字为空，也要触发搜索（显示热门话题或空结果）
      if (this.topicPopover.loading) {
        // 如果正在加载，取消之前的请求（这里简单处理，实际可以用AbortController）
        this.topicPopover.loading = false
      }

      this.topicPopover.loading = true
      try {
        const response = await this.request.getTopic({
          action: '201023',
          title: keyword,
          highlightTitle: 'title',
          page: this.topicPopover.searchPage,
          limit: 50
        })

        if (response && response.data && response.data.code === 0) {
          const topics = response.data.data || []
          this.topicPopover.searchTopics = topics
          this.topicPopover.searchPage++

          if (topics.length === 0) {
            this.topicPopover.searchHasMore = false
          } else {
            this.topicPopover.searchHasMore = topics.length >= 50 // 如果返回满页，可能还有更多
          }
        } else {
          console.warn('搜索话题失败:', response)
          this.topicPopover.searchHasMore = false
        }
      } catch (error) {
        console.error('搜索话题失败:', error)
        this.topicPopover.searchHasMore = false
      } finally {
        this.topicPopover.loading = false
        // 更新全局弹框显示
        this.updateGlobalTopicPopover()
      }
    },

    loadRecentTopics() {
      try {
        const localTopics = JSON.parse(localStorage.getItem('localTopic') || '[]')
        this.topicPopover.recentTopics = localTopics
      } catch (error) {
        console.error('加载最近使用话题失败:', error)
        this.topicPopover.recentTopics = []
      }
    },

    selectTopic(topic) {
      // 保存到最近使用
      this.saveToRecentTopics(topic)

      // 插入话题到编辑器
      this.insertTopicToEditor(topic)

      // 隐藏弹框
      this.hideTopicPopover()

      // 触发更新事件
      this.$emit('topic-inserted', topic)
    },

    saveToRecentTopics(topic) {
      try {
        let recentTopics = JSON.parse(localStorage.getItem('localTopic') || '[]')

        // 移除已存在的相同话题
        recentTopics = recentTopics.filter(item => item.id !== topic.id)

        // 添加到开头
        recentTopics.unshift(topic)

        // 限制最多保存20个
        if (recentTopics.length > 20) {
          recentTopics = recentTopics.slice(0, 20)
        }

        localStorage.setItem('localTopic', JSON.stringify(recentTopics))
      } catch (error) {
        console.error('保存最近使用话题失败:', error)
      }
    },

    // 创建全局话题弹框
    createGlobalTopicPopover() {
      // 如果已经存在，先移除
      this.removeGlobalTopicPopover()

      // 创建遮罩层
      const mask = document.createElement('div')
      mask.className = 'topic-popover-mask'
      mask.addEventListener('click', () => {
        this.hideTopicPopover()
      })

      // 创建弹框容器
      const container = document.createElement('div')
      container.className = 'topic-popover'

      // 使用fixed定位，相对于视口
      container.style.position = 'fixed'
      container.style.top = this.topicPopover.position.top + 'px'
      container.style.left = this.topicPopover.position.left + 'px'
      container.style.zIndex = '9999'
      container.addEventListener('click', (e) => {
        e.stopPropagation()
      })

      // 生成弹框内容
      container.innerHTML = this.createTopicPopoverContent()

      // 添加到body
      document.body.appendChild(mask)
      document.body.appendChild(container)

      // 保存引用
      this.topicPopover.globalMask = mask
      this.topicPopover.globalContainer = container

      // 绑定事件
      this.bindTopicPopoverEvents()
    },

    // 移除全局话题弹框
    removeGlobalTopicPopover() {
      if (this.topicPopover.globalMask) {
        document.body.removeChild(this.topicPopover.globalMask)
        this.topicPopover.globalMask = null
      }
      if (this.topicPopover.globalContainer) {
        document.body.removeChild(this.topicPopover.globalContainer)
        this.topicPopover.globalContainer = null
      }
    },

    // 更新全局话题弹框内容
    updateGlobalTopicPopover() {
      if (!this.topicPopover.globalContainer) return

      this.topicPopover.globalContainer.innerHTML = this.createTopicPopoverContent()
      this.bindTopicPopoverEvents()
    },

    // 创建话题弹框内容
    createTopicPopoverContent() {
      if (this.topicPopover.type === 'hot') {
        return this.getHotTopicPopoverHTML()
      } else if (this.topicPopover.type === 'search') {
        return this.getSearchTopicPopoverHTML()
      }
      return ''
    },

    // 获取热门话题弹框HTML
    getHotTopicPopoverHTML() {
      const activeTab = this.topicPopover.activeTab
      const hotTopics = this.topicPopover.hotTopics
      const recentTopics = this.topicPopover.recentTopics
      const loading = this.topicPopover.loading
      const hasMore = this.topicPopover.hasMore

      let topicListHTML = ''
      if (activeTab === 'hot') {
        if (loading && hotTopics.length === 0) {
          topicListHTML = '<div class="topic-loading">加载中...</div>'
        } else if (hotTopics.length === 0) {
          topicListHTML = '<div class="topic-empty">暂无热门话题</div>'
        } else {
          topicListHTML = hotTopics.map(topic =>
            `<div class="topic-item" data-topic-id="${topic.id}">#${topic.exactlyMatchTitle}</div>`
          ).join('')

          if (loading) {
            topicListHTML += '<div class="topic-loading">加载更多...</div>'
          } else if (!hasMore) {
            topicListHTML += '<div class="topic-empty">没有更多了</div>'
          }
        }
      } else {
        if (recentTopics.length === 0) {
          topicListHTML = '<div class="topic-empty">暂无最近使用记录</div>'
        } else {
          topicListHTML = recentTopics.map(topic =>
            `<div class="topic-item" data-topic-id="${topic.id}">#${topic.exactlyMatchTitle}</div>`
          ).join('')
        }
      }

      return `
        <div class="topic-popover-content">
          <div class="topic-tabs">
            <div class="topic-tab ${activeTab === 'hot' ? 'active' : ''}" data-tab="hot">
              热门话题
            </div>
            <div class="topic-tab ${activeTab === 'recent' ? 'active' : ''}" data-tab="recent">
              最近使用
            </div>
          </div>
          <div class="topic-list-container">
            <div class="topic-list">
              ${topicListHTML}
            </div>
          </div>
        </div>
      `
    },

    // 获取搜索话题弹框HTML
    getSearchTopicPopoverHTML() {
      const searchKeyword = this.topicPopover.searchKeyword
      const searchTopics = this.topicPopover.searchTopics
      const loading = this.topicPopover.loading
      const searchHasMore = this.topicPopover.searchHasMore

      let topicListHTML = ''
      if (loading && searchTopics.length === 0) {
        topicListHTML = '<div class="topic-loading">搜索中...</div>'
      } else if (searchTopics.length === 0) {
        topicListHTML = '<div class="topic-empty">没有匹配到话题，请重新输入</div>'
      } else {
        topicListHTML = searchTopics.map(topic =>
          `<div class="topic-item" data-topic-id="${topic.id}">#${topic.exactlyMatchTitle}</div>`
        ).join('')

        if (loading) {
          topicListHTML += '<div class="topic-loading">加载更多...</div>'
        } else if (!searchHasMore) {
          topicListHTML += '<div class="topic-empty">没有更多了</div>'
        }
      }

      return `
        <div class="topic-popover-content">
          <div class="topic-search-header">
            <span class="topic-search-title">#${searchKeyword}</span>
          </div>
          <div class="topic-list-container">
            <div class="topic-list">
              ${topicListHTML}
            </div>
          </div>
        </div>
      `
    },

    // 绑定话题弹框事件
    bindTopicPopoverEvents() {
      if (!this.topicPopover.globalContainer) return

      // 绑定标签切换事件
      const tabs = this.topicPopover.globalContainer.querySelectorAll('.topic-tab')
      tabs.forEach(tab => {
        tab.addEventListener('click', (e) => {
          const tabType = e.target.getAttribute('data-tab')
          this.switchTopicTab(tabType)
        })
      })

      // 绑定话题选择事件
      const topicItems = this.topicPopover.globalContainer.querySelectorAll('.topic-item')
      topicItems.forEach(item => {
        item.addEventListener('click', (e) => {
          const topicId = parseInt(e.target.getAttribute('data-topic-id'))
          const topic = this.findTopicById(topicId)
          if (topic) {
            this.selectTopic(topic)
          }
        })
      })

      // 绑定滚动事件
      const listContainer = this.topicPopover.globalContainer.querySelector('.topic-list-container')
      if (listContainer) {
        listContainer.addEventListener('scroll', (e) => {
          if (this.topicPopover.type === 'hot') {
            this.handleTopicScroll(e)
          } else if (this.topicPopover.type === 'search') {
            this.handleSearchTopicScroll(e)
          }
        })
      }
    },

    // 根据ID查找话题
    findTopicById(topicId) {
      // 在热门话题中查找
      let topic = this.topicPopover.hotTopics.find(t => t.id === topicId)
      if (topic) return topic

      // 在搜索话题中查找
      topic = this.topicPopover.searchTopics.find(t => t.id === topicId)
      if (topic) return topic

      // 在最近使用中查找
      topic = this.topicPopover.recentTopics.find(t => t.id === topicId)
      if (topic) return topic

      return null
    },

    handleTopicScroll(event) {
      if (this.topicPopover.activeTab !== 'hot') return

      const container = event.target
      const scrollTop = container.scrollTop
      const scrollHeight = container.scrollHeight
      const clientHeight = container.clientHeight

      // 滚动到底部时加载更多
      if (scrollTop + clientHeight >= scrollHeight - 10) {
        this.loadHotTopics()
      }
    },

    handleSearchTopicScroll(event) {
      const container = event.target
      const scrollTop = container.scrollTop
      const scrollHeight = container.scrollHeight
      const clientHeight = container.clientHeight

      // 滚动到底部时加载更多
      if (scrollTop + clientHeight >= scrollHeight - 10) {
        this.searchTopics(this.topicPopover.searchKeyword)
      }
    },

    insertTopicToEditor(topic) {
      // 先隐藏弹框，避免range变化
      const originalRange = this.topicPopover.originalRange
      const triggerInfo = this.topicPopover.triggerInfo
      const popoverType = this.topicPopover.type

      this.hideTopicPopover()

      // 使用保存的原始range或当前selection
      let range = originalRange
      if (!range) {
        const selection = window.getSelection()
        if (selection.rangeCount === 0) return
        range = selection.getRangeAt(0)
      }

      const topicText = `#${topic.exactlyMatchTitle}`

      // 根据触发信息删除已输入的内容
      if (popoverType === 'search' && triggerInfo && triggerInfo.paragraph) {
        const paragraph = triggerInfo.paragraph
        const hashIndex = triggerInfo.hashIndex

        // 创建新的range来删除从#号开始的内容
        const deleteRange = document.createRange()
        const walker = document.createTreeWalker(
          paragraph,
          NodeFilter.SHOW_TEXT,
          {
            acceptNode: function(node) {
              // 跳过mdd-topic元素内的文本节点
              let parent = node.parentNode
              while (parent && parent !== paragraph) {
                if (parent.tagName === 'MDD-TOPIC') {
                  return NodeFilter.FILTER_REJECT
                }
                parent = parent.parentNode
              }
              return NodeFilter.FILTER_ACCEPT
            }
          },
          false
        )

        let position = 0
        let startNode = null
        let startOffset = 0
        let endNode = null
        let endOffset = 0
        let node

        // 找到#号的位置
        while (node = walker.nextNode()) {
          if (position + node.textContent.length > hashIndex) {
            startNode = node
            startOffset = hashIndex - position
            break
          }
          position += node.textContent.length
        }

        // 找到当前光标位置
        const currentCursorPosition = triggerInfo.cursorPosition
        position = 0
        walker.currentNode = paragraph

        while (node = walker.nextNode()) {
          if (position + node.textContent.length >= currentCursorPosition) {
            endNode = node
            endOffset = currentCursorPosition - position
            break
          }
          position += node.textContent.length
        }

        if (startNode && endNode) {
          deleteRange.setStart(startNode, startOffset)
          deleteRange.setEnd(endNode, endOffset)
          deleteRange.deleteContents()

          // 设置新的插入位置
          range = document.createRange()
          range.setStart(startNode, startOffset)
          range.collapse(true)
        }
      } else if (popoverType === 'hot') {
        // 热门话题模式，需要删除刚输入的#号
        const container = range.startContainer
        if (container.nodeType === Node.TEXT_NODE && container.textContent) {
          const offset = range.startOffset
          if (offset > 0 && container.textContent[offset - 1] === '#') {
            // 删除#号
            const deleteRange = document.createRange()
            deleteRange.setStart(container, offset - 1)
            deleteRange.setEnd(container, offset)
            deleteRange.deleteContents()

            // 更新range位置
            range.setStart(container, offset - 1)
            range.collapse(true)
          }
        }
      }

      // 创建话题元素
      const topicElement = document.createElement('mdd-topic')
      topicElement.setAttribute('data-topic', JSON.stringify({
        topicId: topic.id,
        topicType: 0,
        startIndex: 0, // 会在updateTopicPosition中更新
        endIndex: 0    // 会在updateTopicPosition中更新
      }))
      topicElement.textContent = topicText

      // 设置mdd-topic元素为不可编辑
      topicElement.setAttribute('contenteditable', 'false')

      // 删除选中的内容
      range.deleteContents()

      // 插入话题元素
      range.insertNode(topicElement)

      // 在话题后面插入一个空格（使用&nbsp;）
      const spaceNode = document.createTextNode('\u00A0') // 不间断空格
      range.setStartAfter(topicElement)
      range.insertNode(spaceNode)

      // 设置光标位置到空格后面
      const newRange = document.createRange()
      newRange.setStartAfter(spaceNode)
      newRange.collapse(true)

      // 恢复selection
      const selection = window.getSelection()
      selection.removeAllRanges()
      selection.addRange(newRange)

      // 使用setTimeout确保DOM更新完成后再设置光标
      setTimeout(() => {
        const currentSelection = window.getSelection()
        if (currentSelection.rangeCount > 0) {
          const currentRange = currentSelection.getRangeAt(0)

          // 确保光标在正确位置，但不添加额外内容
          if (currentRange.startContainer !== spaceNode.nextSibling) {
            const correctRange = document.createRange()
            correctRange.setStartAfter(spaceNode)
            correctRange.collapse(true)
            currentSelection.removeAllRanges()
            currentSelection.addRange(correctRange)
          }
        }
      }, 0)

      // 触发话题位置更新事件
      this.$emit('update-topic-position')
    },

    // 销毁话题管理器
    destroy() {
      this.unbindEditorEvents()
      this.hideTopicPopover()
    }
  },

  mounted() {
    this.init()
  },

  beforeUnmount() {
    this.destroy()
  }
}
</script>

<style lang="less">
@import './index.less';
</style>
