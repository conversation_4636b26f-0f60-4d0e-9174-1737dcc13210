# ------------------------------------------------------------------
mark
MAIN-48030   // 新代码分支
backup f8075c0      // 备份代码

话题操作 核心逻辑在 keydown 的以下事件判断中
'Enter' // 回车
'Backspace', 'Delete' // 删除
'ArrowLeft' // 左
# ------------------------------------------------------------------
功能
① video文章字段、剩余字数，重新写
② 待定：在有话题的段落halo-paragraph粘贴文字，内容都被添加到了下一行，希望短话题前后也可以粘贴文字

问题
keydown 中处理逻辑
1、在第二行文字行首按删除时，会先删除第一行的话题
话题 话题 话题 文字
文字(af 光标前)
`
<p class="halo-paragraph"><mdd-topic data-topic="{&quot;topicId&quot;:677,&quot;topicType&quot;:0,&quot;startIndex&quot;:0,&quot;endIndex&quot;:7}" contenteditable="false">#推荐话题6<br></mdd-topic>&nbsp;<mdd-topic data-topic="{&quot;topicId&quot;:677,&quot;topicType&quot;:0,&quot;startIndex&quot;:0,&quot;endIndex&quot;:7}" contenteditable="false">#推荐话题6<br></mdd-topic>&nbsp;<mdd-topic data-topic="{&quot;topicId&quot;:675,&quot;topicType&quot;:0,&quot;startIndex&quot;:16,&quot;endIndex&quot;:23}" contenteditable="false">#推荐话题5<br></mdd-topic>&nbsp;sdfsdf</p>
<p class="halo-paragraph">af<br></p>
`
2、arrowleft
`
<p class="halo-paragraph"><mdd-topic data-topic="{&quot;topicId&quot;:677,&quot;topicType&quot;:0,&quot;startIndex&quot;:0,&quot;endIndex&quot;:6}" contenteditable="false">#推荐话题6</mdd-topic>&nbsp;<mdd-topic data-topic="{&quot;topicId&quot;:666,&quot;topicType&quot;:0,&quot;startIndex&quot;:7,&quot;endIndex&quot;:13}" contenteditable="false">#推荐话题4</mdd-topic><span style="font-size: 17px;display: inline-block;">&nbsp;</span><mdd-topic data-topic="{&quot;topicId&quot;:666,&quot;topicType&quot;:0,&quot;startIndex&quot;:7,&quot;endIndex&quot;:13}" contenteditable="false">#推荐话题4</mdd-topic><span style="font-size: 17px;display: inline-block;">&nbsp;</span></p>
`
里面的<span style="font-size: 17px;display: inline-block;">&nbsp;</span>也需要支持光标定位
# ------------------------------------------------------------------

keydown里面 Backspace、Delete 逻辑重构：
dom 结构示例
`
<p class="halo-paragraph"><br></p>
<p class="halo-paragraph">23<b>42342</b>34<br></p>
<h2 class="halo-paragraph-title">rwerwerwer<br></h2>
<p class="halo-paragraph">地表最聪明的<mdd-topic data-topic={"topicId":"12345","topicType":0,"startIndex":"12","endIndex":"16"}>#123</mdd-topic>&nbsp;动力心脏</p>
<p class="halo-paragraph">daf dsf<mdd-topic data-topic="{&quot;topicId&quot;:677,&quot;topicType&quot;:0,&quot;startIndex&quot;:0,&quot;endIndex&quot;:6}" contenteditable="false">#推荐话题6</mdd-topic>&nbsp;<mdd-topic data-topic="{&quot;topicId&quot;:675,&quot;topicType&quot;:0,&quot;startIndex&quot;:7,&quot;endIndex&quot;:13}" contenteditable="false">#推荐话题5</mdd-topic>sdfadf sdfadsf<mdd-topic data-topic="{&quot;topicId&quot;:666,&quot;topicType&quot;:0,&quot;startIndex&quot;:14,&quot;endIndex&quot;:20}" contenteditable="false">#推荐话题4</mdd-topic>&nbsp;dsa fdsf</p>
<ol>
  <li>233342<br></li>
  <li>23423<br></li>
</ol>
<halo-article data-article="{&quot;id&quot;:5675033}"></halo-article>
<div class="halo-link" contenteditable="false"><a class="halo-link-mes" data-url="www.baidu.com" target="_blank">2323</a></div>
<div class="halo-img-content" contenteditable="false">
  <img id=""
    src="http://imgs2.58moto.com/forum/20220909/3e44260d22404af8b3014487c9142871.jpeg!nowater?_516_1015"
    class="halo-picture-area"
    data="{&quot;src&quot;:&quot;http://imgs2.58moto.com/forum/20220909/3e44260d22404af8b3014487c9142871.jpeg!nowater?_516_1015&quot;,&quot;img&quot;:&quot;http://imgs2.58moto.com/forum/20220909/3e44260d22404af8b3014487c9142871.jpeg!nowater?_516_1015&quot;,&quot;selected&quot;:false,&quot;type&quot;:&quot;2&quot;}"
    data-content="" contenteditable="false" data-desc="232323">
</div>
`

# ------------------------------------------------------------------
